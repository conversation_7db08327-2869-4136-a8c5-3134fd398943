<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\UserStatusEnum;
use App\Models\Student;
use App\Models\User;
use App\Models\Classroom;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Requests\Requests\StudentRequests\StudentStoreRequest;
use App\Http\Requests\Requests\StudentRequests\StudentUpdateRequest;

class StudentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->datatables($request);
        }

        return view('admin.students.index', [
            'genders' => GenderEnum::options(),
            'classrooms' => Classroom::pluck('name', 'id'),
            'statuses' => UserStatusEnum::options(),
        ]);
    }

    public function datatables($req)
    {
        $query = Student::with(['user', 'classrooms']);

        return DataTables::eloquent($query)
            ->addIndexColumn()
            ->addColumn('name', function ($row) {
                return $row->user->name;
            })
            ->addColumn('email', function ($row) {
                return $row->user->email;
            })
            ->addColumn('phone_number', function ($row) {
                return $row->user->phone_number;
            })
            ->addColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-white">' . $row->user->status->label() . '</span>';
            })
            ->editColumn('gender', function ($row) {
                return '<span class="badge bg-' . $row->gender->color() . ' text-white">' . $row->gender->label() . '</span>';
            })
            ->addColumn('classroom', function ($row) {
                $classrooms = $row->classrooms->pluck('name')->toArray();
                return !empty($classrooms) ? implode(', ', $classrooms) : '<span class="text-muted">-</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.students._action', [
                    'edit' => route('admin.students.edit', $row->id),
                    'destroy' => route('admin.students.destroy', $row->id),
                    'id' => $row->id,
                ]);
            })
            // Filter kolom gender
            ->filterColumn('gender', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->where('gender', $keyword);
                }
            })
            // Filter kolom classroom
            ->filterColumn('classroom', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('classrooms', function ($query) use ($keyword) {
                        $query->where('classroom_id', $keyword);
                    });
                }
            })
            // Filter kolom status
            ->filterColumn('status', function ($q, $keyword) {
                if (!empty($keyword) && $keyword !== 'all') {
                    $q->whereHas('user', function ($query) use ($keyword) {
                        $query->where('status', $keyword);
                    });
                }
            })
            // Search global untuk semua kolom
            ->filter(function ($q) use ($req) {
                if ($search = $req->input('search.value')) {
                    $q->where(function ($sub) use ($search) {
                        $sub->whereHas('user', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%")
                                ->orWhere('phone_number', 'like', "%{$search}%");
                        })
                            ->orWhere('nis', 'like', "%{$search}%")
                            ->orWhere('nisn', 'like', "%{$search}%")
                            ->orWhere('birth_place', 'like', "%{$search}%")
                            ->orWhere('parent_name', 'like', "%{$search}%")
                            ->orWhere('parent_phone', 'like', "%{$search}%");
                    });
                }
            })
            ->rawColumns(['action', 'gender', 'status', 'classroom'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.students.create', [
            'genders' => GenderEnum::options(),
            'statuses' => UserStatusEnum::options(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StudentStoreRequest $request)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Format tanggal untuk birth_date.
        if (isset($data['birth_date'])) {
            $data['birth_date'] = Carbon::parse($data['birth_date'])->toDateString();
        }

        // Memisahkan data user dan student
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'username' => $data['username'],
            'password' => bcrypt($data['password']),
            'status' => $data['status'] ?? UserStatusEnum::Active->value,
        ];

        $studentData = [
            'nis' => $data['nis'],
            'nisn' => $data['nisn'],
            'birth_place' => $data['birth_place'],
            'birth_date' => $data['birth_date'],
            'gender' => $data['gender'],
            'parent_name' => $data['parent_name'],
            'parent_phone' => $data['parent_phone'],
        ];

        // Memeriksa apakah siswa dengan NIS/NISN yang sama sudah ada.
        $studentExists = Student::where('nis', $data['nis'])
            ->orWhere('nisn', $data['nisn'])
            ->exists();

        if ($studentExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Siswa dengan NIS atau NISN yang sama sudah ada.');
        }

        // Menggunakan transaction untuk memastikan kedua operasi (user dan student) berhasil
        DB::transaction(function () use ($userData, $studentData) {
            // Buat user baru
            $user = User::create($userData);

            // Assign role siswa
            $user->assignRole('student');

            // Buat data siswa terkait user
            $user->student()->create($studentData);
        });

        return redirect()->route('admin.students.index')
            ->with('success', 'Data siswa berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $student = Student::with('user', 'classrooms')->findOrFail($id);

        return view('admin.students.edit', [
            'student' => $student,
            'genders' => GenderEnum::options(),
            'statuses' => UserStatusEnum::options(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StudentUpdateRequest $request, string $id)
    {
        // Mengambil data yang sudah divalidasi dari request.
        $data = $request->validated();

        // Mencari data siswa berdasarkan ID.
        $student = Student::with('user')->findOrFail($id);

        // Format tanggal untuk birth_date.
        if (isset($data['birth_date'])) {
            $data['birth_date'] = Carbon::parse($data['birth_date'])->toDateString();
        }

        // Memeriksa apakah siswa dengan NIS/NISN yang sama sudah ada,
        // tetapi mengecualikan siswa yang sedang di-update.
        $studentExists = Student::where(function ($query) use ($data) {
            $query->where('nis', $data['nis'])
                ->orWhere('nisn', $data['nisn']);
        })
            ->where('id', '!=', $id)
            ->exists();

        // Jika data duplikat ditemukan, kembalikan dengan pesan error.
        if ($studentExists) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Siswa dengan NIS atau NISN yang sama sudah ada.');
        }

        // Memisahkan data user dan student
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'],
            'username' => $data['username'],
            'status' => $data['status'] ?? $student->user->status->value,
        ];

        // Jika password diisi, update password
        if (!empty($data['password'])) {
            $userData['password'] = bcrypt($data['password']);
        }

        $studentData = [
            'nis' => $data['nis'],
            'nisn' => $data['nisn'],
            'birth_place' => $data['birth_place'],
            'birth_date' => $data['birth_date'],
            'gender' => $data['gender'],
            'parent_name' => $data['parent_name'],
            'parent_phone' => $data['parent_phone'],
        ];

        // Menggunakan transaction untuk memastikan pembaruan data berjalan dengan aman.
        DB::transaction(function () use ($student, $userData, $studentData) {
            // Update data user
            $student->user->update($userData);

            // Update data siswa
            $student->update($studentData);
        });

        return redirect()->route('admin.students.index')
            ->with('success', 'Data siswa berhasil diubah.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $student = Student::findOrFail($id);
        $userId = $student->user_id;

        // Menggunakan transaction untuk memastikan kedua operasi (delete student dan user) berhasil
        DB::transaction(function () use ($student, $userId) {
            // Hapus data siswa
            $student->delete();

            // Hapus data user terkait
            User::findOrFail($userId)->delete();
        });

        return response()->json([
            'message' => 'Data siswa berhasil dihapus.'
        ], 200);
    }
}
