<?php

use App\Http\Controllers\Admin\{
    AcademicYear<PERSON>ontroller,
    ClassroomController,
    ProgramController,
    SubjectController,
    Teacher<PERSON><PERSON>roller,
    StudentController,
    ShiftController,
    TeacherAssignmentController,
    ClassScheduleController,
    LeaveRequestController,
    SubstitutionRecordController,
    ConfigurationController,
    <PERSON>r<PERSON><PERSON>roll<PERSON>,
    TeacherAttendanceController,
    StudentAttendanceController as AdminStudentAttendanceController,
    JournalController,
    AdminDashboardController
};

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::redirect('/', 'login', 301);

Auth::routes();

Route::middleware(['auth'])->group(function () {

    // Admin Routes
    Route::prefix('admin')->name('admin.')->middleware(['role:admin'])->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        Route::resource('academic_years', AcademicYearController::class);
        Route::resource('subjects', SubjectController::class);
        Route::resource('shifts', ShiftController::class);
        Route::resource('programs', ProgramController::class);
        // Route::resource('classrooms', ClassroomController::class);
        // Route::resource('teachers', TeacherController::class);
        Route::resource('students', StudentController::class);
        Route::resource('configurations', ConfigurationController::class);
        // Route::resource('teacher_assignments', TeacherAssignmentController::class);
        // Route::resource('class_schedules', ClassScheduleController::class);
        // Route::resource('leave_requests', LeaveRequestController::class);
        // Route::resource('substitution_records', SubstitutionRecordController::class);
        // Route::resource('users', UserController::class);
        // Route::get('teacher_attendances/reports', [TeacherAttendanceController::class, 'reports'])->name('admin.teacher_attendances.reports');
        // Route::get('student_attendances/reports', [AdminStudentAttendanceController::class, 'reports'])->name('admin.student_attendances.reports');
        // Route::get('journals/reports', [JournalController::class, 'reports'])->name('admin.journals.reports');
    });
});

